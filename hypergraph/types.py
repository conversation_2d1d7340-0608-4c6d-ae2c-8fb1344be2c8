from dataclasses import dataclass
from typing import Protocol, TypedDict, Union, Literal, runtime_checkable, Optional


class TextChunkSchema(TypedDict, total=False):
    tokens: int
    content: str
    full_doc_id: str
    chunk_order_index: int


@dataclass
class QueryParam:
    mode: Literal["local", "global", "hybrid", "naive"] = "hybrid"
    only_need_context: bool = False
    only_need_prompt: bool = False
    response_type: str = "Multiple Paragraphs"
    stream: bool = False
    top_k: int = 60
    max_token_for_text_unit: int = 4000
    max_token_for_global_context: int = 4000
    max_token_for_local_context: int = 4000


@dataclass
class ChunkConfig:
    enabled: bool = False
    overlap_token_size: int = 128
    max_token_size: int = 1024
    tiktoken_model: str = "gpt-4o"


@runtime_checkable
class GraphStorage(Protocol):
    async def has_node(self, node_id: str) -> bool: ...
    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool: ...
    async def node_degree(self, node_id: str) -> int: ...
    async def edge_degree(self, src_id: str, tgt_id: str) -> int: ...
    async def get_node(self, node_id: str) -> Union[dict, None]: ...
    async def get_edge(self, source_node_id: str, target_node_id: str) -> Union[dict, None]: ...
    async def get_node_edges(self, source_node_id: str) -> Union[list[tuple[str, str]], None]: ...
    async def upsert_node(self, node_id: str, node_data: dict): ...
    async def upsert_edge(self, source_node_id: str, target_node_id: str, edge_data: dict): ...


@runtime_checkable
class VectorStorage(Protocol):
    async def query(self, query: str, top_k: int) -> list[dict]: ...
    async def upsert(self, data: dict[str, dict]): ...


@runtime_checkable
class KVStorage(Protocol):
    async def all_keys(self) -> list[str]: ...
    async def get_by_id(self, id: str) -> Union[TextChunkSchema, None]: ...
    async def get_by_ids(self, ids: list[str], fields: set[str] | None = None) -> list[Union[TextChunkSchema, None]]: ...
    async def upsert(self, data: dict[str, TextChunkSchema]): ...


@runtime_checkable
class CacheKV(Protocol):
    async def get_by_id(self, id: str) -> Optional[dict]: ...
    async def upsert(self, data: dict[str, dict]): ...

