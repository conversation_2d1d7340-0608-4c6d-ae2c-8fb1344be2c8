from __future__ import annotations
from dataclasses import dataclass
from typing import Dict, Tuple, Union, Optional, List

from ..types import GraphStorage, VectorStorage, KVStorage, TextChunkSchema


@dataclass
class MemoryKVStorage(KVStorage):
    def __post_init__(self):
        self._data: Dict[str, TextChunkSchema] = {}

    async def all_keys(self) -> list[str]:
        return list(self._data.keys())

    async def get_by_id(self, id: str) -> Union[TextChunkSchema, None]:
        return self._data.get(id)

    async def get_by_ids(self, ids: list[str], fields: set[str] | None = None) -> list[Union[TextChunkSchema, None]]:
        if fields is None:
            return [self._data.get(i) for i in ids]
        out = []
        for i in ids:
            v = self._data.get(i)
            if v is None:
                out.append(None)
            else:
                out.append({k: v[k] for k in v.keys() & fields})
        return out

    async def upsert(self, data: dict[str, TextChunkSchema]):
        self._data.update(data)


@dataclass
class MemoryVectorStorage(VectorStorage):
    def __post_init__(self):
        self._data: Dict[str, Dict] = {}

    async def query(self, query: str, top_k: int) -> list[dict]:
        # trivial implementation – returns first top_k entries
        items = list(self._data.items())[:top_k]
        return [{"id": k, **v} for k, v in items]

    async def upsert(self, data: dict[str, dict]):
        self._data.update(data)


@dataclass
class MemoryGraphStorage(GraphStorage):
    def __post_init__(self):
        self._nodes: Dict[str, Dict] = {}
        self._edges: Dict[Tuple[str, str], Dict] = {}

    async def has_node(self, node_id: str) -> bool:
        return node_id in self._nodes

    async def has_edge(self, source_node_id: str, target_node_id: str) -> bool:
        return (source_node_id, target_node_id) in self._edges

    async def node_degree(self, node_id: str) -> int:
        deg = 0
        for (s, t) in self._edges.keys():
            if s == node_id or t == node_id:
                deg += 1
        return deg

    async def edge_degree(self, src_id: str, tgt_id: str) -> int:
        return await self.node_degree(src_id) + await self.node_degree(tgt_id)

    async def get_node(self, node_id: str) -> Union[dict, None]:
        return self._nodes.get(node_id)

    async def get_edge(self, source_node_id: str, target_node_id: str) -> Union[dict, None]:
        return self._edges.get((source_node_id, target_node_id))

    async def get_node_edges(self, source_node_id: str) -> Union[list[tuple[str, str]], None]:
        if source_node_id not in self._nodes:
            return None
        return [(s, t) for (s, t) in self._edges.keys() if s == source_node_id]

    async def upsert_node(self, node_id: str, node_data: dict):
        if node_id in self._nodes:
            self._nodes[node_id].update(node_data)
        else:
            self._nodes[node_id] = dict(node_data)

    async def upsert_edge(self, source_node_id: str, target_node_id: str, edge_data: dict):
        if (source_node_id, target_node_id) in self._edges:
            self._edges[(source_node_id, target_node_id)].update(edge_data)
        else:
            self._edges[(source_node_id, target_node_id)] = dict(edge_data)

