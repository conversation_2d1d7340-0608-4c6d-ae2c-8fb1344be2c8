import asyncio
from .prompts import PROM<PERSON><PERSON> as _<PERSON>OM<PERSON><PERSON>


def build_extraction_prompt(
    input_text: str,
    language: str = _PROMPTS["DEFAULT_LANGUAGE"],
    examples: str = "",
    tuple_delimiter: str = _PROMPTS["DEFAULT_TUPLE_DELIMITER"],
    record_delimiter: str = _PROMPTS["DEFAULT_RECORD_DELIMITER"],
    completion_delimiter: str = _PROMPTS["DEFAULT_COMPLETION_DELIMITER"],
) -> str:
    ctx = dict(
        tuple_delimiter=tuple_delimiter,
        record_delimiter=record_delimiter,
        completion_delimiter=completion_delimiter,
        examples=examples,
        language=language,
    )
    return _PROMPTS["entity_extraction"].format(**ctx, input_text="{input_text}").format(
        **ctx, input_text=input_text
    )


import re
from collections import Counter, defaultdict
from typing import Union

from .prompts import GRAPH_FIELD_SEP, PROMP<PERSON>
from .types import GraphStorage, VectorStorage, KVStorage, TextChunkSchema, QueryParam
from .utils import clean_str, is_float_regex, split_string_by_multi_markers, compute_mdhash_id


async def _handle_single_entity_extraction(record_attributes: list[str], chunk_key: str, now_hyper_relation: str):
    if len(record_attributes) < 5 or record_attributes[0] != '"entity"' or now_hyper_relation == "":
        return None
    entity_name = clean_str(record_attributes[1].upper())
    if not entity_name.strip():
        return None
    entity_type = clean_str(record_attributes[2].upper())
    entity_description = clean_str(record_attributes[3])
    weight = float(record_attributes[-1]) if is_float_regex(record_attributes[-1]) else 50.0
    return dict(
        entity_name=entity_name,
        entity_type=entity_type,
        description=entity_description,
        weight=weight,
        hyper_relation=now_hyper_relation,
        source_id=chunk_key,
    )


async def _handle_single_hyperrelation_extraction(record_attributes: list[str], chunk_key: str):
    if len(record_attributes) < 3 or record_attributes[0] != '"hyper-relation"':
        return None
    knowledge_fragment = clean_str(record_attributes[1])
    edge_source_id = chunk_key
    weight = float(record_attributes[-1]) if is_float_regex(record_attributes[-1]) else 1.0
    return dict(
        hyper_relation="<hyperedge>" + knowledge_fragment,
        weight=weight,
        source_id=edge_source_id,
    )


async def _merge_hyperedges_then_upsert(hyperedge_name: str, nodes_data: list[dict], knowledge_graph_inst: GraphStorage):
    already_weights: list[float] = []
    already_source_ids: list[str] = []

    already_hyperedge = await knowledge_graph_inst.get_node(hyperedge_name)
    if already_hyperedge is not None:
        already_weights.append(already_hyperedge.get("weight", 0))
        already_source_ids.extend(split_string_by_multi_markers(already_hyperedge.get("source_id", ""), [GRAPH_FIELD_SEP]))

    weight = sum([dp["weight"] for dp in nodes_data] + already_weights)
    source_id = GRAPH_FIELD_SEP.join(set([dp["source_id"] for dp in nodes_data] + already_source_ids))
    node_data = dict(role="hyperedge", weight=weight, source_id=source_id)
    await knowledge_graph_inst.upsert_node(hyperedge_name, node_data=node_data)
    node_data["hyperedge_name"] = hyperedge_name
    return node_data


async def _merge_nodes_then_upsert(
    entity_name: str,
    nodes_data: list[dict],
    knowledge_graph_inst: GraphStorage,
    language: str,
    llm_func=None,
    do_summary: bool = False,
):
    already_entity_types: list[str] = []
    already_source_ids: list[str] = []
    already_description: list[str] = []

    already_node = await knowledge_graph_inst.get_node(entity_name)
    if already_node is not None:
        already_entity_types.append(already_node.get("entity_type", ""))
        already_source_ids.extend(split_string_by_multi_markers(already_node.get("source_id", ""), [GRAPH_FIELD_SEP]))
        already_description.append(already_node.get("description", ""))

    entity_type = sorted(
        Counter([dp["entity_type"] for dp in nodes_data] + already_entity_types).items(),
        key=lambda x: x[1],
        reverse=True,
    )[0][0]
    description = GRAPH_FIELD_SEP.join(
        sorted(set([dp["description"] for dp in nodes_data] + already_description))
    )
    source_id = GRAPH_FIELD_SEP.join(
        set([dp["source_id"] for dp in nodes_data] + already_source_ids)
    )

    # 可选摘要
    if do_summary and llm_func is not None and description:
        from .prompts import PROMPTS as P
        prompt = P["summarize_entity_descriptions"].format(
            entity_name=entity_name, description_list=description.split(GRAPH_FIELD_SEP), language=language
        )
        try:
            description = await llm_func(prompt, max_tokens=512)
        except Exception:
            pass

    node_data = dict(
        role="entity",
        entity_type=entity_type,
        description=description,
        source_id=source_id,
    )
    await knowledge_graph_inst.upsert_node(entity_name, node_data=node_data)
    node_data["entity_name"] = entity_name
    return node_data


async def _merge_edges_then_upsert(entity_name: str, nodes_data: list[dict], knowledge_graph_inst: GraphStorage):
    edge_data = []
    for node in nodes_data:
        source_id = node["source_id"]
        hyper_relation = node["hyper_relation"]
        weight = node["weight"]

        already_weights: list[float] = []
        already_source_ids: list[str] = []

        if await knowledge_graph_inst.has_edge(hyper_relation, entity_name):
            already_edge = await knowledge_graph_inst.get_edge(hyper_relation, entity_name)
            if already_edge:
                already_weights.append(already_edge.get("weight", 0))
                already_source_ids.extend(split_string_by_multi_markers(already_edge.get("source_id", ""), [GRAPH_FIELD_SEP]))

        weight = sum([weight] + already_weights)
        source_id = GRAPH_FIELD_SEP.join(set([source_id] + already_source_ids))

        await knowledge_graph_inst.upsert_edge(hyper_relation, entity_name, edge_data=dict(weight=weight, source_id=source_id))
        edge_data.append(dict(src_id=hyper_relation, tgt_id=entity_name, weight=weight))
    return edge_data


async def extract_entities(
    chunks: dict[str, TextChunkSchema],
    knowledge_graph_inst: GraphStorage,
    entity_vdb: VectorStorage | None,
    hyperedge_vdb: VectorStorage | None,
    language: str = PROMPTS["DEFAULT_LANGUAGE"],
    llm_func=None,
    do_summary: bool = False,
) -> Union[GraphStorage, None]:
    # 如果 do_summary=True 且注入了 llm_func，会为实体描述做一次摘要归并
    ordered_chunks = list(chunks.items())

    async def _process_single_content(chunk_key_dp: tuple[str, TextChunkSchema]):
        chunk_key, chunk_dp = chunk_key_dp
        content = chunk_dp["content"]
        # The content is expected to be the raw LLM extraction output in our tuple/record format.
        final_result = content
        records = split_string_by_multi_markers(final_result, [PROMPTS["DEFAULT_RECORD_DELIMITER"], PROMPTS["DEFAULT_COMPLETION_DELIMITER"]])

        maybe_nodes = defaultdict(list)
        maybe_edges = defaultdict(list)
        now_hyper_relation = ""
        for record in records:
            m = re.search(r"\((.*)\)", record)
            if m is None:
                continue
            record = m.group(1)
            record_attributes = split_string_by_multi_markers(record, [PROMPTS["DEFAULT_TUPLE_DELIMITER"]])
            if_relation = await _handle_single_hyperrelation_extraction(record_attributes, chunk_key)
            if if_relation is not None:
                maybe_edges[if_relation["hyper_relation"]].append(if_relation)
                now_hyper_relation = if_relation["hyper_relation"]
            if_entities = await _handle_single_entity_extraction(record_attributes, chunk_key, now_hyper_relation)
            if if_entities is not None:
                maybe_nodes[if_entities["entity_name"]].append(if_entities)
        return dict(maybe_nodes), dict(maybe_edges)

    results = await asyncio.gather(*[_process_single_content(c) for c in ordered_chunks])

    maybe_nodes = defaultdict(list)
    maybe_edges = defaultdict(list)
    for m_nodes, m_edges in results:
        for k, v in m_nodes.items():
            maybe_nodes[k].extend(v)
        for k, v in m_edges.items():
            maybe_edges[k].extend(v)

    all_hyperedges_data = []
    for k, v in maybe_edges.items():
        all_hyperedges_data.append(await _merge_hyperedges_then_upsert(k, v, knowledge_graph_inst))

    all_entities_data = []
    for k, v in maybe_nodes.items():
        all_entities_data.append(
            await _merge_nodes_then_upsert(
                k, v, knowledge_graph_inst, language, llm_func=llm_func, do_summary=do_summary
            )
        )

    for k, v in maybe_nodes.items():
        await _merge_edges_then_upsert(k, v, knowledge_graph_inst)

    if hyperedge_vdb is not None:
        data_for_vdb = {compute_mdhash_id(dp["hyperedge_name"], prefix="rel-"): {"content": dp["hyperedge_name"], "hyperedge_name": dp["hyperedge_name"]} for dp in all_hyperedges_data}
        await hyperedge_vdb.upsert(data_for_vdb)

    if entity_vdb is not None:
        data_for_vdb = {compute_mdhash_id(dp["entity_name"], prefix="ent-"): {"content": dp["entity_name"] + dp["description"], "entity_name": dp["entity_name"]} for dp in all_entities_data}
        await entity_vdb.upsert(data_for_vdb)

    return knowledge_graph_inst


async def _find_most_related_edges_from_entities(node_datas: list[dict], knowledge_graph_inst: GraphStorage):
    all_related_edges = await asyncio.gather(*[knowledge_graph_inst.get_node_edges(dp["entity_name"]) for dp in node_datas])
    all_edges: list[tuple[str, str]] = []
    seen = set()
    for this_edges in all_related_edges:
        if not this_edges:
            continue
        for e in this_edges:
            sorted_edge = tuple(e)
            if sorted_edge not in seen:
                seen.add(sorted_edge)
                all_edges.append(sorted_edge)
    packs = await asyncio.gather(*[knowledge_graph_inst.get_edge(e[0], e[1]) for e in all_edges])
    degrees = await asyncio.gather(*[knowledge_graph_inst.edge_degree(e[0], e[1]) for e in all_edges])
    data = [{"src_tgt": k, "rank": d, "description": k[1], **v} for k, v, d in zip(all_edges, packs, degrees) if v is not None]
    data = sorted(data, key=lambda x: (x["rank"], x.get("weight", 0)), reverse=True)
    return data


async def kg_query(
    query: str,
    knowledge_graph_inst: GraphStorage,
    entities_vdb: list[str],
    hyperedges_vdb: list[str],
    text_chunks_db: KVStorage,
    query_param: QueryParam,
) -> list[dict]:
    # The portable version assumes entities_vdb/hyperedges_vdb are already matched ids
    keywords = [query, query]
    context = await _build_query_context(keywords, knowledge_graph_inst, entities_vdb, hyperedges_vdb, text_chunks_db, query_param)
    return context


async def _build_query_context(query: list, knowledge_graph_inst: GraphStorage, entities_vdb: list[str], hyperedges_vdb: list[str], text_chunks_db: KVStorage, query_param: QueryParam):
    ll_kewwords, hl_keywrds = query[0], query[1]
    knowledge_list_1 = await _get_node_data(ll_kewwords, knowledge_graph_inst, entities_vdb, text_chunks_db, query_param)
    knowledge_list_2 = await _get_edge_data(hl_keywrds, knowledge_graph_inst, hyperedges_vdb, text_chunks_db, query_param)
    know_score: dict[str, float] = {}
    for i, k in enumerate(knowledge_list_1):
        know_score.setdefault(k, 0.0)
        know_score[k] += 1 / (i + 1)
    for i, k in enumerate(knowledge_list_2):
        know_score.setdefault(k, 0.0)
        know_score[k] += 1 / (i + 1)
    knowledge_list = sorted(know_score.items(), key=lambda x: x[1], reverse=True)[: query_param.top_k]
    knowledge: list[dict] = []
    for k in knowledge_list:
        knowledge.append({"<knowledge>": k[0], "<coherence>": round(k[1], 3)})
    return knowledge


async def _get_node_data(query: str, knowledge_graph_inst: GraphStorage, entities_vdb: list[str], text_chunks_db: KVStorage, query_param: QueryParam):
    results = entities_vdb
    if not len(results):
        return []
    node_datas = await asyncio.gather(*[knowledge_graph_inst.get_node(r) for r in results])
    node_degrees = await asyncio.gather(*[knowledge_graph_inst.node_degree(r) for r in results])
    node_datas = [{**n, "entity_name": k, "rank": d} for k, n, d in zip(results, node_datas, node_degrees) if n is not None]
    use_relations = await _find_most_related_edges_from_entities(node_datas, knowledge_graph_inst)
    knowledge_list = [s["description"].replace("<hyperedge>", "") for s in use_relations]
    return knowledge_list


async def _get_edge_data(keywords: str, knowledge_graph_inst: GraphStorage, hyperedges_vdb: list[str], text_chunks_db: KVStorage, query_param: QueryParam):
    results = hyperedges_vdb
    if not len(results):
        return []
    edge_datas = await asyncio.gather(*[knowledge_graph_inst.get_node(r) for r in results])
    edge_datas = [{"hyperedge": k, "rank": v.get("weight", 0), **v} for k, v in zip(results, edge_datas) if v is not None]
    edge_datas = sorted(edge_datas, key=lambda x: (x["rank"], x.get("weight", 0)), reverse=True)
    knowledge_list = [s["hyperedge"].replace("<hyperedge>", "") for s in edge_datas]
    return knowledge_list



async def aextract_from_raw_texts(
    docs: list[dict],
    knowledge_graph_inst: GraphStorage,
    entity_vdb: VectorStorage | None,
    hyperedge_vdb: VectorStorage | None,
    llm_func,
    language: str = PROMPTS["DEFAULT_LANGUAGE"],
    entity_extract_max_gleaning: int = 2,
):
    """
    End-to-end: from raw docs to hypergraph.
    docs: list of {"id": str, "content": str}
    llm_func: async(str, max_tokens?: int, history_messages?: list[dict]) -> str
    """
    # For simplicity here, we skip tiktoken-based chunking; callers can pre-chunk if needed.
    chunks: dict[str, TextChunkSchema] = {}

    for dp in docs:
        chunk_id = compute_mdhash_id(dp["content"].strip(), prefix="chunk-")
        chunks[chunk_id] = {"content": dp["content"].strip(), "full_doc_id": dp.get("id", ""), "tokens": 0, "chunk_order_index": 0}

    # Build prompts, call LLM with gleaning loop
    continue_prompt = PROMPTS.get("entiti_continue_extraction", "")
    if_loop_prompt = PROMPTS.get("entiti_if_loop_extraction", "")

    async def _run_single(chunk_key: str, content: str):
        hint_prompt = build_extraction_prompt(content, language=language)
        final_result = await llm_func(hint_prompt)
        history = [{"role": "user", "content": hint_prompt}, {"role": "assistant", "content": final_result}]
        for _ in range(entity_extract_max_gleaning):
            if not continue_prompt or not if_loop_prompt:
                break
            glean_result = await llm_func(continue_prompt, history_messages=history)
            history += [{"role": "user", "content": continue_prompt}, {"role": "assistant", "content": glean_result}]
            final_result += glean_result
            if_loop_result = await llm_func(if_loop_prompt, history_messages=history)
            if str(if_loop_result).strip().strip('\"\'').lower() != "yes":
                break
        return chunk_key, {"content": final_result, "full_doc_id": chunks[chunk_key].get("full_doc_id", ""), "tokens": 0, "chunk_order_index": 0}

    processed = await asyncio.gather(*[_run_single(k, v["content"]) for k, v in chunks.items()])
    processed_chunks = {k: v for k, v in processed}

    # Reuse the existing extraction based on processed content
    return await extract_entities(processed_chunks, knowledge_graph_inst, entity_vdb, hyperedge_vdb, language)


async def aextract_from_raw_texts_plus(
    docs: list[dict],
    knowledge_graph_inst: GraphStorage,
    entity_vdb: VectorStorage | None,
    hyperedge_vdb: VectorStorage | None,
    llm_func,
    language: str = PROMPTS["DEFAULT_LANGUAGE"],
    entity_extract_max_gleaning: int = 2,
    chunk_config: "ChunkConfig | None" = None,
    hashing_kv=None,
):
    """
    Enhanced end-to-end extraction with optional chunking and cache.
    - docs: list of {"id": str, "content": str}
    - llm_func: async(str, max_tokens?: int, history_messages?: list[dict]) -> str
    - chunk_config: optional ChunkConfig for approximate char-length chunking (no external deps)
    - hashing_kv: optional cache KV (must provide get_by_id/upsert)
    """
    from .types import ChunkConfig
    from .utils import compute_args_hash, cache_lookup, cache_save

    chunks: dict[str, TextChunkSchema] = {}

    def _default_chunking():
        for dp in docs:
            chunk_id = compute_mdhash_id(dp["content"].strip(), prefix="chunk-")
            chunks[chunk_id] = {
                "content": dp["content"].strip(),
                "full_doc_id": dp.get("id", ""),
                "tokens": 0,
                "chunk_order_index": 0,
            }

    if chunk_config and getattr(chunk_config, "enabled", False):
        # Approximate token-based chunking using characters (~4 chars per token)
        max_len = max(64, int(chunk_config.max_token_size * 4))
        overlap = int(chunk_config.overlap_token_size * 4)
        for dp in docs:
            text = dp["content"].strip()
            start = 0
            order = 0
            while start < len(text):
                end = min(len(text), start + max_len)
                sub = text[start:end]
                chunk_id = compute_mdhash_id(f"{dp.get('id','')}-{order}-{sub}", prefix="chunk-")
                chunks[chunk_id] = {
                    "content": sub,
                    "full_doc_id": dp.get("id", ""),
                    "tokens": 0,
                    "chunk_order_index": order,
                }
                order += 1
                if end >= len(text):
                    break
                start = end - overlap if overlap > 0 else end
    else:
        _default_chunking()

    continue_prompt = PROMPTS.get("entiti_continue_extraction", "")
    if_loop_prompt = PROMPTS.get("entiti_if_loop_extraction", "")

    async def _run_single(chunk_key: str, content: str):
        hint_prompt = build_extraction_prompt(content, language=language)
        args_hash = compute_args_hash("extract", language, content)

        cached = await cache_lookup(hashing_kv, mode="extract", args_hash=args_hash)
        if cached is not None:
            final_result = cached
            history = []
        else:
            final_result = await llm_func(hint_prompt)
            history = [
                {"role": "user", "content": hint_prompt},
                {"role": "assistant", "content": final_result},
            ]
            for _ in range(entity_extract_max_gleaning):
                if not continue_prompt or not if_loop_prompt:
                    break
                glean_result = await llm_func(continue_prompt, history_messages=history)
                history += [
                    {"role": "user", "content": continue_prompt},
                    {"role": "assistant", "content": glean_result},
                ]
                final_result += glean_result
                if_loop_result = await llm_func(if_loop_prompt, history_messages=history)
                if str(if_loop_result).strip().strip('\"\'').lower() != "yes":
                    break
            await cache_save(
                hashing_kv,
                mode="extract",
                args_hash=args_hash,
                content=final_result,
                prompt=hint_prompt,
            )

        return chunk_key, {
            "content": final_result,
            "full_doc_id": chunks[chunk_key].get("full_doc_id", ""),
            "tokens": 0,
            "chunk_order_index": chunks[chunk_key].get("chunk_order_index", 0),
        }

    processed = await asyncio.gather(
        *[_run_single(k, v["content"]) for k, v in chunks.items()]
    )
    processed_chunks = {k: v for k, v in processed}

    return await extract_entities(
        processed_chunks,
        knowledge_graph_inst,
        entity_vdb,
        hyperedge_vdb,
        language,
        llm_func=llm_func,
        do_summary=True,
    )


# Overloaded aextract_from_raw_texts with chunking+cache is not applied here; see separate version if needed.
