# Copyright (c) Microsoft Corporation.
# Licensed under the MIT license.

from typing import List, Optional, Union, Dict, Any
from enum import Enum
import spacy
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_core.documents import Document

from pikerag.llm_client import BaseLLMClient
from pikerag.prompts import CommunicationProtocol
from pikerag.utils.logger import Logger


class ChunkingMethod(Enum):
    """Enumeration of available chunking methods."""
    RECURSIVE_CHARACTER = "recursive_character"
    SENTENCE_BASED = "sentence_based"
    LLM_POWERED = "llm_powered"


class ChunkingConfig:
    """Configuration class for chunking parameters."""
    
    def __init__(
        self,
        method: ChunkingMethod = ChunkingMethod.RECURSIVE_CHARACTER,
        chunk_size: int = 4000,
        chunk_overlap: int = 200,
        separators: Optional[List[str]] = None,
        # Sentence-based specific parameters
        lang: str = "en",
        nlp_max_len: int = 4000000,
        # LLM-powered specific parameters
        llm_client: Optional[BaseLLMClient] = None,
        first_chunk_summary_protocol: Optional[CommunicationProtocol] = None,
        last_chunk_summary_protocol: Optional[CommunicationProtocol] = None,
        chunk_resplit_protocol: Optional[CommunicationProtocol] = None,
        llm_config: Optional[Dict[str, Any]] = None,
        # Common parameters
        logger: Optional[Logger] = None,
        **kwargs
    ):
        self.method = method
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.separators = separators or ["\n\n", "\n", " ", ""]
        self.lang = lang
        self.nlp_max_len = nlp_max_len
        self.llm_client = llm_client
        self.first_chunk_summary_protocol = first_chunk_summary_protocol
        self.last_chunk_summary_protocol = last_chunk_summary_protocol
        self.chunk_resplit_protocol = chunk_resplit_protocol
        self.llm_config = llm_config or {}
        self.logger = logger
        self.kwargs = kwargs


def chunk_text(
    text: str,
    config: ChunkingConfig,
    metadata: Optional[Dict[str, Any]] = None
) -> List[str]:
    """
    Split text into chunks using the specified chunking method.
    
    Args:
        text: The input text to be chunked
        config: ChunkingConfig object containing chunking parameters
        metadata: Optional metadata dictionary for the text
        
    Returns:
        List of text chunks
        
    Raises:
        ValueError: If required parameters are missing for the selected method
        NotImplementedError: If the chunking method is not supported
    """
    if config.method == ChunkingMethod.RECURSIVE_CHARACTER:
        return _chunk_text_recursive_character(text, config)
    elif config.method == ChunkingMethod.SENTENCE_BASED:
        return _chunk_text_sentence_based(text, config)
    elif config.method == ChunkingMethod.LLM_POWERED:
        return _chunk_text_llm_powered(text, config, metadata)
    else:
        raise NotImplementedError(f"Chunking method {config.method} is not implemented")


def chunk_documents(
    documents: List[Document],
    config: ChunkingConfig
) -> List[Document]:
    """
    Split a list of documents into chunks using the specified chunking method.
    
    Args:
        documents: List of Document objects to be chunked
        config: ChunkingConfig object containing chunking parameters
        
    Returns:
        List of chunked Document objects
    """
    if config.method == ChunkingMethod.RECURSIVE_CHARACTER:
        return _chunk_documents_recursive_character(documents, config)
    elif config.method == ChunkingMethod.SENTENCE_BASED:
        return _chunk_documents_sentence_based(documents, config)
    elif config.method == ChunkingMethod.LLM_POWERED:
        return _chunk_documents_llm_powered(documents, config)
    else:
        raise NotImplementedError(f"Chunking method {config.method} is not implemented")


def _chunk_text_recursive_character(text: str, config: ChunkingConfig) -> List[str]:
    """Split text using RecursiveCharacterTextSplitter."""
    splitter = RecursiveCharacterTextSplitter(
        separators=config.separators,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        **config.kwargs
    )
    return splitter.split_text(text)


def _chunk_documents_recursive_character(
    documents: List[Document], 
    config: ChunkingConfig
) -> List[Document]:
    """Split documents using RecursiveCharacterTextSplitter."""
    splitter = RecursiveCharacterTextSplitter(
        separators=config.separators,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        **config.kwargs
    )
    return splitter.split_documents(documents)


def _chunk_text_sentence_based(text: str, config: ChunkingConfig) -> List[str]:
    """Split text using sentence-based chunking with spaCy."""
    from pikerag.document_transformers.splitter.recursive_sentence_splitter import (
        RecursiveSentenceSplitter, LANG2MODELNAME
    )
    
    # Validate language support
    if config.lang not in LANG2MODELNAME:
        raise ValueError(f"Language '{config.lang}' is not supported. "
                        f"Supported languages: {list(LANG2MODELNAME.keys())}")
    
    splitter = RecursiveSentenceSplitter(
        lang=config.lang,
        nlp_max_len=config.nlp_max_len,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        **config.kwargs
    )
    return splitter.split_text(text)


def _chunk_documents_sentence_based(
    documents: List[Document], 
    config: ChunkingConfig
) -> List[Document]:
    """Split documents using sentence-based chunking with spaCy."""
    from pikerag.document_transformers.splitter.recursive_sentence_splitter import (
        RecursiveSentenceSplitter, LANG2MODELNAME
    )
    
    # Validate language support
    if config.lang not in LANG2MODELNAME:
        raise ValueError(f"Language '{config.lang}' is not supported. "
                        f"Supported languages: {list(LANG2MODELNAME.keys())}")
    
    splitter = RecursiveSentenceSplitter(
        lang=config.lang,
        nlp_max_len=config.nlp_max_len,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        **config.kwargs
    )
    
    texts = [doc.page_content for doc in documents]
    metadatas = [doc.metadata for doc in documents]
    return splitter.create_documents(texts, metadatas)


def _chunk_text_llm_powered(
    text: str,
    config: ChunkingConfig,
    metadata: Optional[Dict[str, Any]] = None
) -> List[str]:
    """Split text using LLM-powered recursive splitter."""
    # Validate required parameters
    if not config.llm_client:
        raise ValueError("llm_client is required for LLM-powered chunking")
    if not config.first_chunk_summary_protocol:
        raise ValueError("first_chunk_summary_protocol is required for LLM-powered chunking")
    if not config.last_chunk_summary_protocol:
        raise ValueError("last_chunk_summary_protocol is required for LLM-powered chunking")
    if not config.chunk_resplit_protocol:
        raise ValueError("chunk_resplit_protocol is required for LLM-powered chunking")

    from pikerag.document_transformers.splitter.llm_powered_recursive_splitter import (
        LLMPoweredRecursiveSplitter
    )

    splitter = LLMPoweredRecursiveSplitter(
        llm_client=config.llm_client,
        first_chunk_summary_protocol=config.first_chunk_summary_protocol,
        last_chunk_summary_protocol=config.last_chunk_summary_protocol,
        chunk_resplit_protocol=config.chunk_resplit_protocol,
        llm_config=config.llm_config,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        logger=config.logger,
        **config.kwargs
    )

    metadata = metadata or {}
    return splitter.split_text(text, metadata)


def _chunk_documents_llm_powered(
    documents: List[Document],
    config: ChunkingConfig
) -> List[Document]:
    """Split documents using LLM-powered recursive splitter."""
    # Validate required parameters
    if not config.llm_client:
        raise ValueError("llm_client is required for LLM-powered chunking")
    if not config.first_chunk_summary_protocol:
        raise ValueError("first_chunk_summary_protocol is required for LLM-powered chunking")
    if not config.last_chunk_summary_protocol:
        raise ValueError("last_chunk_summary_protocol is required for LLM-powered chunking")
    if not config.chunk_resplit_protocol:
        raise ValueError("chunk_resplit_protocol is required for LLM-powered chunking")

    from pikerag.document_transformers.splitter.llm_powered_recursive_splitter import (
        LLMPoweredRecursiveSplitter
    )

    splitter = LLMPoweredRecursiveSplitter(
        llm_client=config.llm_client,
        first_chunk_summary_protocol=config.first_chunk_summary_protocol,
        last_chunk_summary_protocol=config.last_chunk_summary_protocol,
        chunk_resplit_protocol=config.chunk_resplit_protocol,
        llm_config=config.llm_config,
        chunk_size=config.chunk_size,
        chunk_overlap=config.chunk_overlap,
        logger=config.logger,
        **config.kwargs
    )

    return splitter.split_documents(documents)


def chunk_by_sentences(text: str, lang: str = "en") -> List[str]:
    """
    Simple sentence-based chunking function using spaCy.

    Args:
        text: The input text to be chunked
        lang: Language code ("en" for English, "zh" for Chinese)

    Returns:
        List of sentences

    Raises:
        ValueError: If the language is not supported
    """
    LANG2MODELNAME = {
        "en": "en_core_web_lg",
        "zh": "zh_core_web_lg",
    }

    if lang not in LANG2MODELNAME:
        raise ValueError(f"Language '{lang}' is not supported. "
                        f"Supported languages: {list(LANG2MODELNAME.keys())}")

    model_name = LANG2MODELNAME[lang]
    try:
        nlp = spacy.load(model_name)
    except OSError:
        import spacy.cli
        spacy.cli.download(model_name)
        nlp = spacy.load(model_name)

    doc = nlp(text)
    sentences = [sent.text.strip() for sent in doc.sents]
    return [sent for sent in sentences if len(sent) > 0]


# Convenience functions for common use cases
def simple_chunk_text(
    text: str,
    chunk_size: int = 4000,
    chunk_overlap: int = 200,
    separators: Optional[List[str]] = None
) -> List[str]:
    """
    Simple text chunking using RecursiveCharacterTextSplitter.

    Args:
        text: The input text to be chunked
        chunk_size: Maximum size of each chunk
        chunk_overlap: Number of characters to overlap between chunks
        separators: List of separators to use for splitting

    Returns:
        List of text chunks
    """
    config = ChunkingConfig(
        method=ChunkingMethod.RECURSIVE_CHARACTER,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        separators=separators
    )
    return chunk_text(text, config)


def sentence_chunk_text(
    text: str,
    chunk_size: int = 12,
    chunk_overlap: int = 4,
    lang: str = "en"
) -> List[str]:
    """
    Sentence-based text chunking using spaCy.

    Args:
        text: The input text to be chunked
        chunk_size: Number of sentences per chunk
        chunk_overlap: Number of sentences to overlap between chunks
        lang: Language code ("en" for English, "zh" for Chinese)

    Returns:
        List of text chunks
    """
    config = ChunkingConfig(
        method=ChunkingMethod.SENTENCE_BASED,
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        lang=lang
    )
    return chunk_text(text, config)
