
from collections import Counter
from typing import Any, Dict, List, Optional

from hypergraphx.utils.log import logger


def split_string_by_multi_markers(content: str, markers: list[str]) -> list[str]:
    if not markers:
        return [content]
    import re
    results = re.split("|".join(re.escape(marker) for marker in markers), content)
    return [r.strip() for r in results if r.strip()]


FIELD_SEP = "<SEP>"


def _join_uniq(values: List[str], already: Optional[List[str]] = None) -> str:
    s = set([v for v in values if v])
    if already:
        s.update([v for v in already if v])
    return FIELD_SEP.join(sorted(s))


def _split(s: str) -> List[str]:
    if not s:
        return []
    try:
        return split_string_by_multi_markers(s, [FIELD_SEP])
    except Exception:
        return [x.strip() for x in s.split(FIELD_SEP) if x.strip()]


async def merge_hyperedges_then_upsert(
    hyperedge_name: str,
    nodes_data: List[Dict[str, Any]],
    storage,
) -> Dict[str, Any]:
    """
    Merge and upsert a hyperedge node.

    nodes_data requires fields: weight (float), source_id (str)
    """
    already_weights: List[float] = []
    already_source_ids: List[str] = []

    already = await storage.get_node(hyperedge_name)
    if already is not None:
        already_weights.append(float(already.get("weight", 0)))
        already_source_ids.extend(_split(already.get("source_id", "")))

    weight = sum([float(dp.get("weight", 0)) for dp in nodes_data] + already_weights)
    source_id = _join_uniq([str(dp.get("source_id", "")) for dp in nodes_data], already_source_ids)

    node_data = dict(role="hyperedge", weight=weight, source_id=source_id)
    await storage.upsert_node(hyperedge_name, node_data=node_data)
    node_data["hyperedge_name"] = hyperedge_name
    return node_data


async def merge_nodes_then_upsert(
    entity_name: str,
    nodes_data: List[Dict[str, Any]],
    storage,
    *,
    do_summary: bool = False,
    llm_func=None,
) -> Dict[str, Any]:
    """
    Merge and upsert an entity node.

    nodes_data requires fields: entity_type (str), description (str), source_id (str)
    If do_summary=True and llm_func provided, description will be summarized via llm_func(prompt:str, max_tokens:int) -> str
    """
    already_entity_types: List[str] = []
    already_source_ids: List[str] = []
    already_descriptions: List[str] = []

    already = await storage.get_node(entity_name)
    if already is not None:
        already_entity_types.append(str(already.get("entity_type", "")))
        already_source_ids.extend(_split(already.get("source_id", "")))
        already_descriptions.append(str(already.get("description", "")))

    # entity_type: majority vote from new + existing
    types = [str(dp.get("entity_type", "")) for dp in nodes_data] + already_entity_types
    types = [t for t in types if t]
    entity_type = (
        sorted(Counter(types).items(), key=lambda x: x[1], reverse=True)[0][0]
        if types
        else "UNKNOWN"
    )

    description = _join_uniq([str(dp.get("description", "")) for dp in nodes_data] + already_descriptions)
    source_id = _join_uniq([str(dp.get("source_id", "")) for dp in nodes_data], already_source_ids)

    if do_summary and llm_func is not None and description:
        try:
            # prompt 留白，由调用方自定义
            prompt = description  # you can wrap it externally before passing here
            description = await llm_func(prompt, max_tokens=512)
        except Exception as e:  # pragma: no cover
            logger.warning("entity description summary failed: %s", e)

    node_data = dict(role="entity", entity_type=entity_type, description=description, source_id=source_id)
    await storage.upsert_node(entity_name, node_data=node_data)
    node_data["entity_name"] = entity_name
    return node_data


async def merge_edges_then_upsert(
    entity_name: str,
    nodes_data: List[Dict[str, Any]],
    storage,
) -> List[Dict[str, Any]]:
    """
    Create/merge edges from hyperedge -> entity for all nodes_data.

    Each item requires fields: source_id (str), hyper_relation (str), weight (float)
    """
    out: List[Dict[str, Any]] = []
    for node in nodes_data:
        source_id = str(node.get("source_id", ""))
        hyper_relation = str(node.get("hyper_relation", ""))
        weight = float(node.get("weight", 0))
        if not hyper_relation:
            continue

        already_weights: List[float] = []
        already_source_ids: List[str] = []

        if await storage.has_edge(hyper_relation, entity_name):
            already_edge = await storage.get_edge(hyper_relation, entity_name)
            if already_edge:
                already_weights.append(float(already_edge.get("weight", 0)))
                already_source_ids.extend(_split(already_edge.get("source_id", "")))

        new_weight = sum([weight] + already_weights)
        new_source_id = _join_uniq([source_id], already_source_ids)

        await storage.upsert_edge(
            hyper_relation,
            entity_name,
            edge_data=dict(weight=new_weight, source_id=new_source_id),
        )
        out.append(dict(src_id=hyper_relation, tgt_id=entity_name, weight=new_weight))
    return out

