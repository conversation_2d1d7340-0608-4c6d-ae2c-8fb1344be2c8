import json
from collections import defaultdict
from typing import Any, Dict, List, Tuple

from hypergraphx.utils.log import logger


# 目标输出：
# - maybe_hyperedges: Dict[str, List[Dict[str, Any]]]
# - maybe_entities: Dict[str, List[Dict[str, Any]]]

REQUIRED_FIELDS = {
    "hyperedge": ["type", "name", "source_id"],
    "entity": ["type", "name", "entity_type", "description", "hyper_relation", "source_id"],
}


def _validate_record(obj: Dict[str, Any]) -> Tuple[bool, str]:
    t = obj.get("type", "").strip().lower()
    if t not in ("hyperedge", "entity"):
        return False, f"unknown type: {t}"
    miss = [k for k in REQUIRED_FIELDS[t] if k not in obj]
    if miss:
        return False, f"missing fields: {miss}"
    if t == "hyperedge":
        # 可选 weight
        if "weight" in obj:
            try:
                float(obj["weight"])  # ensure numeric
            except Exception:
                return False, "invalid weight type"
    if t == "entity":
        # 可选 weight
        if "weight" in obj:
            try:
                float(obj["weight"])  # ensure numeric
            except Exception:
                return False, "invalid weight type"
    return True, ""


def parse_json_records(text: str):
    """
    解析 JSON 记录文本，支持两种形态：
    - JSON Lines：多行，每行一个 JSON 对象
    - JSON 数组：整体是一个 list

    目标：将记录归并为 maybe_hyperedges / maybe_entities 两个容器
    """
    maybe_hyperedges: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    maybe_entities: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    text = text.strip()
    if not text:
        return dict(maybe_hyperedges), dict(maybe_entities)

    items: List[Dict[str, Any]] = []

    # 先尝试整体作为 JSON 数组
    try:
        obj = json.loads(text)
        if isinstance(obj, list):
            items = [x for x in obj if isinstance(x, dict)]
        elif isinstance(obj, dict):
            items = [obj]
        else:
            # 回退到行解析
            raise ValueError("not a list/dict")
    except Exception:
        # 尝试 JSON Lines
        for line in text.splitlines():
            line = line.strip()
            if not line:
                continue
            try:
                obj = json.loads(line)
                if isinstance(obj, dict):
                    items.append(obj)
            except Exception:
                logger.warning("skip invalid json line: %s", line[:200])

    bad = 0
    for obj in items:
        ok, reason = _validate_record(obj)
        if not ok:
            bad += 1
            logger.warning("skip invalid record: %s (%s)", str(obj)[:200], reason)
            continue
        t = obj["type"].strip().lower()
        if t == "hyperedge":
            name = str(obj["name"]).strip()
            weight = float(obj.get("weight", 1.0))
            source_id = str(obj.get("source_id", "")).strip()
            if not name:
                bad += 1
                continue
            maybe_hyperedges[name].append(dict(weight=weight, source_id=source_id))
        else:  # entity
            name = str(obj["name"]).strip().upper()
            etype = str(obj["entity_type"]).strip().upper()
            desc = str(obj["description"]).strip()
            hrel = str(obj["hyper_relation"]).strip()
            weight = float(obj.get("weight", 1.0))
            source_id = str(obj.get("source_id", "")).strip()
            if not (name and hrel):
                bad += 1
                continue
            maybe_entities[name].append(
                dict(
                    entity_type=etype,
                    description=desc,
                    source_id=source_id,
                    hyper_relation=hrel,
                    weight=weight,
                )
            )

    if bad:
        logger.info("parse_json_records finished with %d invalid records", bad)

    return dict(maybe_hyperedges), dict(maybe_entities)

