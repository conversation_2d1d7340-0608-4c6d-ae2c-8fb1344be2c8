import os
import json
from dataclasses import dataclass
from typing import Dict, Any

from hypergraphx.utils.log import logger


@dataclass
class JsonKVStorage:
    working_dir: str
    namespace: str

    def __post_init__(self):
        os.makedirs(self.working_dir, exist_ok=True)
        self._file_name = os.path.join(self.working_dir, f"{self.namespace}.json")
        self._data: Dict[str, Any] = self._load_json(self._file_name) or {}
        logger.info("[JsonKVStorage] Load %s with %d keys", self.namespace, len(self._data))

    @staticmethod
    def _load_json(file_name: str):
        if not os.path.exists(file_name):
            return None
        with open(file_name, encoding="utf-8") as f:
            return json.load(f)

    @staticmethod
    def _write_json(obj, file_name: str):
        if not os.path.exists(os.path.dirname(file_name)):
            os.makedirs(os.path.dirname(file_name), exist_ok=True)
        with open(file_name, "w", encoding="utf-8") as f:
            json.dump(obj, f, indent=4, ensure_ascii=False)

    async def all_keys(self) -> list[str]:
        return list(self._data.keys())

    async def filter_keys(self, data: list[str]) -> list[str]:
        return [s for s in data if s not in self._data]

    async def get_by_id(self, id):
        return self._data.get(id, None)

    async def upsert(self, data: Dict[str, Any]):
        left_data = {k: v for k, v in data.items() if k not in self._data}
        self._data.update(left_data)
        return left_data

    async def index_done_callback(self):
        self._write_json(self._data, self._file_name)

