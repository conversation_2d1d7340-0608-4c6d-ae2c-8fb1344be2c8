#!/usr/bin/env python3
"""
可视化已生成的超图（GraphML）
- 将超边(hyperedge)与实体(entity)分层显示，直观展示“超边 → 实体”的关系
- 支持两种布局：bipartite（左右分层）与 spring（力导向）
- 可选择仅展示 Top-K 个超边及其相邻实体，生成清爽小图

示例：
python hypergraphx/examples/visualize_hypergraph.py --input cache --output cache/hypergraph.png --layout bipartite --top-k 20 --label
"""
import os
import argparse
import networkx as nx
import matplotlib.pyplot as plt


def _select_topk_subgraph(G: nx.DiGraph, top_k: int) -> nx.DiGraph:
    if top_k <= 0:
        return G
    # 选取权重/度数最高的超边节点
    hyperedges = [n for n, a in G.nodes(data=True) if a.get("role") == "hyperedge"]
    def score(n):
        a = G.nodes[n]
        # 优先依据权重，其次出度（连接实体数）
        return (float(a.get("weight", 0.0)), G.out_degree(n))
    hyperedges_sorted = sorted(hyperedges, key=score, reverse=True)
    chosen = set(hyperedges_sorted[:top_k])
    # 加入相邻的实体节点
    keep = set()
    for h in chosen:
        keep.add(h)
        for succ in G.successors(h):
            keep.add(succ)
    return G.subgraph(keep).copy()


def _bipartite_layout(G: nx.DiGraph):
    # 左侧：hyperedge；右侧：entity
    hyperedges = [n for n, a in G.nodes(data=True) if a.get("role") == "hyperedge"]
    entities = [n for n, a in G.nodes(data=True) if a.get("role") == "entity"]
    # 垂直位置等距排布
    def _linspace(n):
        if n <= 1:
            return [0.5]
        step = 1.0 / (n + 1)
        return [step * (i + 1) for i in range(n)]
    y_h = _linspace(len(hyperedges))
    y_e = _linspace(len(entities))
    pos = {}
    for i, n in enumerate(hyperedges):
        pos[n] = (0.0, y_h[i])
    for i, n in enumerate(entities):
        pos[n] = (1.0, y_e[i])
    return pos


def _spring_layout(G: nx.DiGraph):
    # 力导向布局；尽量让超边与实体分开
    return nx.spring_layout(G, k=0.8, seed=42)


def visualize(graphml_file: str, output_file: str, layout: str = "bipartite", top_k: int = 0, show_label: bool = False, dpi: int = 200):
    if not os.path.exists(graphml_file):
        raise FileNotFoundError(f"GraphML 文件不存在: {graphml_file}")

    G = nx.read_graphml(graphml_file)
    if not isinstance(G, (nx.DiGraph, nx.MultiDiGraph)):
        G = nx.DiGraph(G)
    elif isinstance(G, nx.MultiDiGraph):
        G = nx.DiGraph(G)

    # 仅展示 Top-K 超边及其相邻实体
    H = _select_topk_subgraph(G, top_k)

    # 颜色/形状配置
    hyper_nodes = [n for n, a in H.nodes(data=True) if a.get("role") == "hyperedge"]
    entity_nodes = [n for n, a in H.nodes(data=True) if a.get("role") == "entity"]

    # 节点大小：超边按权重放大，实体按度数放大
    hyper_sizes = [300 + 200 * float(H.nodes[n].get("weight", 1.0)) for n in hyper_nodes]
    entity_sizes = [300 + 100 * H.degree(n) for n in entity_nodes]

    # 边宽度：与权重相关
    edge_widths = [1.0 + 1.5 * float(d.get("weight", 1.0)) for _, _, d in H.edges(data=True)]

    # 布局
    if layout == "bipartite":
        pos = _bipartite_layout(H)
    else:
        pos = _spring_layout(H)

    plt.figure(figsize=(10, 8), dpi=dpi)
    # 画节点
    nx.draw_networkx_nodes(H, pos, nodelist=hyper_nodes, node_color="#ff6b6b", node_shape="s", node_size=hyper_sizes, alpha=0.9, label="Hyperedges")
    nx.draw_networkx_nodes(H, pos, nodelist=entity_nodes, node_color="#4dabf7", node_shape="o", node_size=entity_sizes, alpha=0.9, label="Entities")
    # 画边
    nx.draw_networkx_edges(H, pos, width=edge_widths, alpha=0.5, arrows=True, arrowstyle="-|>", arrowsize=12, edge_color="#666")

    # 标签
    if show_label:
        labels = {n: n for n in H.nodes()}
        nx.draw_networkx_labels(H, pos, labels=labels, font_size=8)

    # 图例 & 样式
    plt.axis("off")
    plt.legend(scatterpoints=1, loc="lower center", ncol=2, frameon=False, bbox_to_anchor=(0.5, -0.05))
    plt.tight_layout()

    # 保存
    out_dir = os.path.dirname(output_file)
    if out_dir and not os.path.exists(out_dir):
        os.makedirs(out_dir, exist_ok=True)
    plt.savefig(output_file, bbox_inches="tight")
    plt.close()
    print(f"✅ 已保存可视化到: {output_file}")


def main():
    parser = argparse.ArgumentParser(description="可视化 HyperGraphX 生成的超图")
    parser.add_argument("--input", default="cache", help="输入目录（包含 hypergraph.graphml）")
    parser.add_argument("--graphml", help="可选：直接指定 GraphML 文件路径，优先使用")
    parser.add_argument("--output", default="cache/hypergraph.png", help="可视化输出图片路径")
    parser.add_argument("--layout", choices=["bipartite", "spring"], default="bipartite", help="布局类型")
    parser.add_argument("--top-k", type=int, default=0, help="仅展示 Top-K 超边及其相邻实体（0 表示全部）")
    parser.add_argument("--label", action="store_true", help="显示节点标签")
    parser.add_argument("--dpi", type=int, default=200, help="导出图片 DPI")

    args = parser.parse_args()

    graphml_file = args.graphml or os.path.join(args.input, "hypergraph.graphml")
    visualize(graphml_file, args.output, layout=args.layout, top_k=args.top_k, show_label=args.label, dpi=args.dpi)


if __name__ == "__main__":
    main()

