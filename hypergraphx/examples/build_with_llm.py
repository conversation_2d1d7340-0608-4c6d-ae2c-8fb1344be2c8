import os
import asyncio
from hypergraphx.llm.openai_client import OpenAIClient
from hypergraphx.core.llm_templates import make_llm_extractor
from hypergraphx.pipeline.build import BuildConfig, build_hypergraph


def my_build_prompt(raw_text: str) -> str:
    """
    自定义抽取 Prompt 的入口（留白示例）。
    你可以把 raw_text 包进你需要的系统/用户提示中，并约定输出为结构化记录文本。

    例如：输出 JSON Lines 格式。
    """
    return raw_text


async def main():
    cfg = BuildConfig(
        input_file="resources/input_examples/raw_demo.jsonl",
        working_dir="cache",
        tokenizer="cl100k_base",
        chunk_size=1024,
        chunk_overlap=100,
        request_concurrency=20,
        storage_concurrency=8,
        do_summary=False,
    )

    # 使用环境变量配置的 OpenAIClient（OPENAI_API_KEY / OPENAI_BASE_URL）
    llm = OpenAIClient(
        model_name=os.getenv("SYNTHESIZER_MODEL") or os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
        api_key=os.getenv("SYNTHESIZER_API_KEY") or os.getenv("OPENAI_API_KEY"),
        base_url=os.getenv("SYNTHESIZER_BASE_URL") or os.getenv("OPENAI_BASE_URL"),
    )

    llm_func = make_llm_extractor(llm, build_prompt=my_build_prompt)

    await build_hypergraph(cfg, llm_client=llm, llm_func=llm_func)
    print("Done. Graph saved to cache/hypergraph.graphml")


if __name__ == "__main__":
    asyncio.run(main())

