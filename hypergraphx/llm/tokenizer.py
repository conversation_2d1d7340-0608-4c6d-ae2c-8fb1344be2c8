from dataclasses import dataclass
from typing import List

try:
    import tiktoken
    _HAS_TIKTOKEN = True
except Exception:
    _HAS_TIKTOKEN = False


@dataclass
class Tokenizer:
    model_name: str = "cl100k_base"

    def __post_init__(self):
        if not _HAS_TIKTOKEN:
            raise ImportError("tiktoken not installed. Please install it or provide another tokenizer.")
        self.tokenizer = tiktoken.get_encoding(self.model_name)

    def encode_string(self, text: str) -> List[int]:
        return self.tokenizer.encode(text)

    def decode_tokens(self, tokens: List[int]) -> str:
        return self.tokenizer.decode(tokens)

    def chunk_by_token_size(self, content: str, overlap_token_size=128, max_token_size=1024):
        tokens = self.encode_string(content)
        results = []
        for index, start in enumerate(range(0, len(tokens), max_token_size - overlap_token_size)):
            chunk_content = self.decode_tokens(tokens[start : start + max_token_size])
            results.append({
                "tokens": min(max_token_size, len(tokens) - start),
                "content": chunk_content.strip(),
                "chunk_order_index": index,
            })
        return results

