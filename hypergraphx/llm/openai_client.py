import os
from dataclasses import dataclass, field
from typing import List, Optional

from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

try:
    from openai import AsyncOpenAI
    from openai import RateLimitError, APIConnectionError, APITimeoutError
except Exception:  # pragma: no cover
    AsyncOpenAI = None
    class RateLimitError(Exception): ...
    class APIConnectionError(Exception): ...
    class APITimeoutError(Exception): ...

try:
    from hypergraphx.llm.tokenizer import Tokenizer
except Exception:
    Tokenizer = None


@dataclass
class OpenAIClient:
    model_name: str = "gpt-4o-mini"
    api_key: str | None = field(default_factory=lambda: os.getenv("OPENAI_API_KEY"))
    base_url: str | None = field(default_factory=lambda: os.getenv("OPENAI_BASE_URL"))

    system_prompt: str = ""
    seed: Optional[int] = None

    max_tokens: int = 1024
    temperature: float = 0.0

    def __post_init__(self):
        if Async<PERSON><PERSON><PERSON><PERSON> is None:
            raise ImportError("openai package not available. Please install openai>=1.0.0")
        self.client = AsyncOpenAI(api_key=self.api_key, base_url=self.base_url)
        self.token_usage: List[dict] = []

    def _pre_messages(self, text: str, history=None):
        messages = []
        if self.system_prompt:
            messages.append({"role": "system", "content": self.system_prompt})
        if history:
            messages.extend(history)
        messages.append({"role": "user", "content": text})
        return {"messages": messages, "max_tokens": self.max_tokens}

    @retry(
        stop=stop_after_attempt(5),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type((RateLimitError, APIConnectionError, APITimeoutError)),
    )
    async def generate_answer(self, text: str, history: Optional[List[str]] = None) -> str:
        kwargs = self._pre_messages(text, history)
        kwargs["temperature"] = self.temperature

        # 估算 tokens（如果可用）
        if Tokenizer is not None:
            prompt_tokens = 0
            for message in kwargs["messages"]:
                prompt_tokens += len(Tokenizer().encode_string(message["content"]))
            estimated_tokens = prompt_tokens + kwargs["max_tokens"]
        else:
            estimated_tokens = kwargs["max_tokens"]

        completion = await self.client.chat.completions.create(model=self.model_name, **kwargs)
        if hasattr(completion, "usage"):
            self.token_usage.append({
                "prompt_tokens": completion.usage.prompt_tokens,
                "completion_tokens": completion.usage.completion_tokens,
                "total_tokens": completion.usage.total_tokens,
            })
        return completion.choices[0].message.content

