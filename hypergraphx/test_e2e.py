#!/usr/bin/env python3
"""
端到端测试脚本：不依赖真实 LLM，使用 mock 数据验证整个 hypergraphx 流程
"""
import os
import asyncio
import tempfile
import shutil
from pathlib import Path

from hypergraphx.pipeline.build import BuildConfig, build_hypergraph
from hypergraphx.examples.read_hypergraph import main as read_main


# Mock LLM 函数：返回预设的 JSON 记录
async def mock_llm_func(text: str, *_, **__) -> str:
    """
    Mock LLM 函数，根据输入文本返回预设的 JSON 记录
    """
    if "Alice" in text or "Bob" in text:
        return '''{"type":"hyperedge","name":"FRIENDSHIP","source_id":"chunk-001"}
{"type":"entity","name":"ALICE","entity_type":"PERSON","description":"A friendly person","hyper_relation":"FRIENDSHIP","weight":1.0,"source_id":"chunk-001"}
{"type":"entity","name":"BOB","entity_type":"PERSON","description":"Alice's friend","hyper_relation":"FRIENDSHIP","weight":1.0,"source_id":"chunk-001"}'''
    
    elif "company" in text.lower() or "work" in text.lower():
        return '''{"type":"hyperedge","name":"EMPLOYMENT","source_id":"chunk-002"}
{"type":"entity","name":"TECHCORP","entity_type":"COMPANY","description":"A technology company","hyper_relation":"EMPLOYMENT","weight":1.0,"source_id":"chunk-002"}
{"type":"entity","name":"ALICE","entity_type":"PERSON","description":"Works at TechCorp","hyper_relation":"EMPLOYMENT","weight":0.8,"source_id":"chunk-002"}'''
    
    else:
        return '''{"type":"hyperedge","name":"GENERAL_RELATION","source_id":"chunk-default"}
{"type":"entity","name":"UNKNOWN","entity_type":"ENTITY","description":"Default entity","hyper_relation":"GENERAL_RELATION","weight":0.5,"source_id":"chunk-default"}'''


def create_test_data(temp_dir: str) -> str:
    """创建测试用的输入文件"""
    test_file = os.path.join(temp_dir, "test_input.jsonl")
    test_data = [
        {"content": "Alice and Bob are good friends. They met in college."},
        {"content": "Alice works at TechCorp, a leading technology company."},
        {"content": "The weather is nice today."}
    ]
    
    with open(test_file, "w", encoding="utf-8") as f:
        import json
        for item in test_data:
            f.write(json.dumps(item) + "\n")
    
    return test_file


async def test_basic_pipeline():
    """测试基本的构建流程"""
    print("🧪 测试基本构建流程...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 1. 创建测试数据
        input_file = create_test_data(temp_dir)
        working_dir = os.path.join(temp_dir, "output")
        
        # 2. 配置
        cfg = BuildConfig(
            input_file=input_file,
            working_dir=working_dir,
            chunk_size=512,
            chunk_overlap=50,
            request_concurrency=2,
            storage_concurrency=2,
            flush_every_n=0,  # 关闭分段 flush
        )
        
        # 3. 运行构建
        hg = await build_hypergraph(cfg, llm_client=None, llm_func=mock_llm_func)
        
        # 4. 验证输出文件
        graph_file = os.path.join(working_dir, "hypergraph.graphml")
        chunks_file = os.path.join(working_dir, "text_chunks.json")
        docs_file = os.path.join(working_dir, "full_docs.json")
        
        assert os.path.exists(graph_file), f"图文件不存在: {graph_file}"
        assert os.path.exists(chunks_file), f"分块文件不存在: {chunks_file}"
        assert os.path.exists(docs_file), f"文档文件不存在: {docs_file}"
        
        # 5. 验证图内容
        nodes = await hg.get_all_nodes()
        edges = await hg.get_all_edges()
        
        print(f"✅ 图构建成功: {len(nodes)} 个节点, {len(edges)} 条边")
        
        # 检查是否有预期的节点
        node_names = [name for name, _ in nodes]
        assert "ALICE" in node_names, "缺少 ALICE 实体"
        assert "BOB" in node_names, "缺少 BOB 实体"
        assert "FRIENDSHIP" in node_names, "缺少 FRIENDSHIP 超边"
        
        print("✅ 节点验证通过")
        
        # 检查节点属性
        for name, attrs in nodes:
            if name == "ALICE":
                assert attrs.get("role") == "entity", f"ALICE 角色错误: {attrs.get('role')}"
                assert attrs.get("entity_type") == "PERSON", f"ALICE 类型错误: {attrs.get('entity_type')}"
            elif name == "FRIENDSHIP":
                assert attrs.get("role") == "hyperedge", f"FRIENDSHIP 角色错误: {attrs.get('role')}"
                assert "weight" in attrs, "FRIENDSHIP 缺少 weight 属性"
        
        print("✅ 节点属性验证通过")
        
        return working_dir


async def test_flush_pipeline():
    """测试分段 flush 流程"""
    print("🧪 测试分段 flush 流程...")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        input_file = create_test_data(temp_dir)
        working_dir = os.path.join(temp_dir, "output")
        
        cfg = BuildConfig(
            input_file=input_file,
            working_dir=working_dir,
            flush_every_n=1,  # 每 1 个条目就 flush
        )
        
        hg = await build_hypergraph(cfg, llm_client=None, llm_func=mock_llm_func)
        
        # 验证文件存在
        graph_file = os.path.join(working_dir, "hypergraph.graphml")
        assert os.path.exists(graph_file), "分段 flush 后图文件不存在"
        
        nodes = await hg.get_all_nodes()
        print(f"✅ 分段 flush 成功: {len(nodes)} 个节点")


def test_read_graph():
    """测试读取图文件"""
    print("🧪 测试读取图文件...")
    
    # 这里需要一个已存在的图文件，我们先创建一个简单的
    with tempfile.TemporaryDirectory() as temp_dir:
        import networkx as nx
        
        # 创建一个简单的测试图
        G = nx.DiGraph()
        G.add_node("TEST_ENTITY", role="entity", entity_type="TEST", description="Test entity")
        G.add_node("TEST_HYPEREDGE", role="hyperedge", weight=1.0, source_id="test")
        G.add_edge("TEST_HYPEREDGE", "TEST_ENTITY", weight=1.0, source_id="test")
        
        graph_file = os.path.join(temp_dir, "hypergraph.graphml")
        nx.write_graphml(G, graph_file)
        
        # 读取验证
        G2 = nx.read_graphml(graph_file)
        assert G2.number_of_nodes() == 2, f"节点数不匹配: {G2.number_of_nodes()}"
        assert G2.number_of_edges() == 1, f"边数不匹配: {G2.number_of_edges()}"
        
        print("✅ 图文件读取验证通过")


async def test_json_parser():
    """测试 JSON 解析器"""
    print("🧪 测试 JSON 解析器...")
    
    from hypergraphx.core.parser import parse_json_records
    
    # 测试 JSON Lines
    json_lines = '''{"type":"hyperedge","name":"TEST_REL","source_id":"test"}
{"type":"entity","name":"TEST_ENT","entity_type":"TEST","description":"Test","hyper_relation":"TEST_REL","source_id":"test"}'''
    
    maybe_h, maybe_e = parse_json_records(json_lines)
    
    assert "TEST_REL" in maybe_h, "超边解析失败"
    assert "TEST_ENT" in maybe_e, "实体解析失败"
    assert len(maybe_h["TEST_REL"]) == 1, "超边记录数不对"
    assert len(maybe_e["TEST_ENT"]) == 1, "实体记录数不对"
    
    # 测试 JSON 数组
    json_array = '''[
        {"type":"hyperedge","name":"ARRAY_REL","source_id":"test"},
        {"type":"entity","name":"ARRAY_ENT","entity_type":"TEST","description":"Array test","hyper_relation":"ARRAY_REL","source_id":"test"}
    ]'''
    
    maybe_h2, maybe_e2 = parse_json_records(json_array)
    assert "ARRAY_REL" in maybe_h2, "JSON 数组超边解析失败"
    assert "ARRAY_ENT" in maybe_e2, "JSON 数组实体解析失败"
    
    print("✅ JSON 解析器验证通过")


async def test_merge_logic():
    """测试合并逻辑"""
    print("🧪 测试合并逻辑...")
    
    from hypergraphx.core.merge import merge_nodes_then_upsert, merge_hyperedges_then_upsert
    from hypergraphx.storage.networkx_hyper_storage import NetworkXHyperStorage
    
    with tempfile.TemporaryDirectory() as temp_dir:
        storage = NetworkXHyperStorage(temp_dir)
        
        # 测试实体合并
        entity_data = [
            {"entity_type": "PERSON", "description": "First desc", "source_id": "chunk1"},
            {"entity_type": "PERSON", "description": "Second desc", "source_id": "chunk2"},
        ]
        
        result = await merge_nodes_then_upsert("TEST_PERSON", entity_data, storage)
        assert result["entity_name"] == "TEST_PERSON", "实体名称不匹配"
        assert result["entity_type"] == "PERSON", "实体类型不匹配"
        
        # 测试超边合并
        hyperedge_data = [
            {"weight": 1.0, "source_id": "chunk1"},
            {"weight": 2.0, "source_id": "chunk2"},
        ]
        
        result2 = await merge_hyperedges_then_upsert("TEST_HYPEREDGE", hyperedge_data, storage)
        assert result2["hyperedge_name"] == "TEST_HYPEREDGE", "超边名称不匹配"
        assert result2["weight"] == 3.0, f"权重合并错误: {result2['weight']}"
        
        print("✅ 合并逻辑验证通过")


async def main():
    """运行所有测试"""
    print("🚀 开始 hypergraphx 端到端测试\n")
    
    try:
        # 基础组件测试
        await test_json_parser()
        await test_merge_logic()
        test_read_graph()
        
        # 端到端流程测试
        working_dir = await test_basic_pipeline()
        await test_flush_pipeline()
        
        print(f"\n🎉 所有测试通过！")
        print(f"📁 最后一次测试的输出目录: {working_dir}")
        print("💡 你可以手动检查生成的文件:")
        print(f"   - 图文件: {working_dir}/hypergraph.graphml")
        print(f"   - 分块文件: {working_dir}/text_chunks.json")
        print(f"   - 文档文件: {working_dir}/full_docs.json")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
