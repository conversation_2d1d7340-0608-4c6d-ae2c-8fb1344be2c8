#!/usr/bin/env python3
"""
HyperGraphX 正式运行脚本

支持多种运行模式：
1. 从原始文档构建超图（需要 LLM）
2. 从预处理的 JSON 记录构建超图
3. 读取并分析已有的超图文件
"""
import os
import sys
import asyncio
import argparse
from pathlib import Path

from hypergraphx.pipeline.build import BuildConfig, build_hypergraph
from hypergraphx.llm.openai_client import OpenAIClient
from hypergraphx.core.llm_templates import make_llm_extractor
from hypergraphx.utils.log import logger, set_logger


def build_extraction_prompt(raw_text: str) -> str:
    """
    构建抽取 Prompt，输出 JSON Lines 格式
    """
    return f"""You are an expert knowledge graph extractor. Extract entities and hyperedges from the following text.

Output format: JSON Lines (one JSON object per line)
- For hyperedges: {{"type":"hyperedge","name":"RELATION_NAME","source_id":"auto"}}
- For entities: {{"type":"entity","name":"ENTITY_NAME","entity_type":"TYPE","description":"DESCRIPTION","hyper_relation":"RELATION_NAME","weight":1.0,"source_id":"auto"}}

Rules:
1. Extract meaningful relationships as hyperedges
2. Extract all relevant entities under each hyperedge
3. Use UPPERCASE for entity names and relation names
4. Provide clear, concise descriptions
5. One hyperedge can connect multiple entities

Text to analyze:
{raw_text}

Output (JSON Lines only):"""


async def run_with_llm(args):
    """使用 LLM 从原始文档构建超图"""
    print("🤖 使用 LLM 从原始文档构建超图...")
    
    # 检查环境变量
    api_key = args.api_key or os.getenv("OPENAI_API_KEY") or os.getenv("SYNTHESIZER_API_KEY")
    if not api_key:
        print("❌ 错误: 未找到 API Key")
        print("请设置环境变量 OPENAI_API_KEY 或使用 --api-key 参数")
        return False
    
    # 配置 LLM 客户端
    llm = OpenAIClient(
        model_name=args.model,
        api_key=api_key,
        base_url=args.base_url,
        max_tokens=args.max_tokens,
        temperature=args.temperature,
    )
    
    # 创建抽取函数
    llm_func = make_llm_extractor(llm, build_prompt=build_extraction_prompt)
    
    # 构建配置
    cfg = BuildConfig(
        input_file=args.input,
        working_dir=args.output,
        tokenizer=args.tokenizer,
        chunk_size=args.chunk_size,
        chunk_overlap=args.chunk_overlap,
        request_concurrency=args.concurrency,
        storage_concurrency=args.storage_concurrency,
        flush_every_n=args.flush_every_n,
        do_summary=args.do_summary,
    )
    
    print(f"📁 输入文件: {args.input}")
    print(f"📁 输出目录: {args.output}")
    print(f"🔧 分块大小: {args.chunk_size}, 重叠: {args.chunk_overlap}")
    print(f"⚡ 并发数: {args.concurrency}")
    
    # 运行构建
    try:
        hg = await build_hypergraph(cfg, llm_client=llm, llm_func=llm_func)
        
        # 统计结果
        nodes = await hg.get_all_nodes()
        edges = await hg.get_all_edges()
        
        entities = [n for n, attrs in nodes if attrs.get("role") == "entity"]
        hyperedges = [n for n, attrs in nodes if attrs.get("role") == "hyperedge"]
        
        print(f"\n✅ 构建完成!")
        print(f"📊 统计信息:")
        print(f"   - 实体: {len(entities)} 个")
        print(f"   - 超边: {len(hyperedges)} 个")
        print(f"   - 连接: {len(edges)} 条")
        print(f"📁 输出文件:")
        print(f"   - 超图: {args.output}/hypergraph.graphml")
        print(f"   - 分块: {args.output}/text_chunks.json")
        print(f"   - 文档: {args.output}/full_docs.json")
        
        return True
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def run_from_json(args):
    """从预处理的 JSON 记录构建超图"""
    print("📄 从 JSON 记录构建超图...")
    
    # 使用空的 LLM 函数（输入已经是 JSON 记录）
    async def identity_func(text: str, *_, **__):
        return text
    
    cfg = BuildConfig(
        input_file=args.input,
        working_dir=args.output,
        chunk_size=args.chunk_size,
        chunk_overlap=args.chunk_overlap,
        storage_concurrency=args.storage_concurrency,
        flush_every_n=args.flush_every_n,
    )
    
    try:
        hg = await build_hypergraph(cfg, llm_client=None, llm_func=identity_func)
        
        nodes = await hg.get_all_nodes()
        edges = await hg.get_all_edges()
        
        entities = [n for n, attrs in nodes if attrs.get("role") == "entity"]
        hyperedges = [n for n, attrs in nodes if attrs.get("role") == "hyperedge"]
        
        print(f"✅ 构建完成!")
        print(f"📊 实体: {len(entities)}, 超边: {len(hyperedges)}, 连接: {len(edges)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 构建失败: {e}")
        return False


def analyze_graph(args):
    """分析已有的超图文件"""
    print("🔍 分析超图文件...")
    
    graph_file = os.path.join(args.input, "hypergraph.graphml")
    if not os.path.exists(graph_file):
        print(f"❌ 图文件不存在: {graph_file}")
        return False
    
    try:
        import networkx as nx
        
        G = nx.read_graphml(graph_file)
        
        # 统计信息
        total_nodes = G.number_of_nodes()
        total_edges = G.number_of_edges()
        
        entities = []
        hyperedges = []
        
        for node, attrs in G.nodes(data=True):
            role = attrs.get("role", "unknown")
            if role == "entity":
                entities.append((node, attrs))
            elif role == "hyperedge":
                hyperedges.append((node, attrs))
        
        print(f"📊 图统计:")
        print(f"   - 总节点: {total_nodes}")
        print(f"   - 总边数: {total_edges}")
        print(f"   - 实体: {len(entities)}")
        print(f"   - 超边: {len(hyperedges)}")
        
        # 显示示例
        if args.show_examples:
            print(f"\n📝 实体示例 (前5个):")
            for name, attrs in entities[:5]:
                etype = attrs.get("entity_type", "N/A")
                desc = attrs.get("description", "")[:50]
                print(f"   - {name} ({etype}): {desc}...")
            
            print(f"\n🔗 超边示例 (前5个):")
            for name, attrs in hyperedges[:5]:
                weight = attrs.get("weight", "N/A")
                print(f"   - {name} (权重: {weight})")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False


def main():
    parser = argparse.ArgumentParser(
        description="HyperGraphX - 超图构建与分析工具",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:

1. 使用 LLM 从原始文档构建超图:
   python hypergraphx/run.py llm --input data.jsonl --output ./output --api-key YOUR_KEY

2. 从 JSON 记录构建超图:
   python hypergraphx/run.py json --input records.jsonl --output ./output

3. 分析已有超图:
   python hypergraphx/run.py analyze --input ./output --show-examples

环境变量:
   OPENAI_API_KEY    - OpenAI API 密钥
   OPENAI_BASE_URL   - OpenAI API 基础URL (可选)
        """
    )
    
    subparsers = parser.add_subparsers(dest="mode", help="运行模式")
    
    # LLM 模式
    llm_parser = subparsers.add_parser("llm", help="使用 LLM 从原始文档构建超图")
    llm_parser.add_argument("--input", required=True, help="输入文件 (.jsonl/.json/.txt)")
    llm_parser.add_argument("--output", default="./output", help="输出目录")
    llm_parser.add_argument("--api-key", help="OpenAI API Key")
    llm_parser.add_argument("--base-url", help="OpenAI API Base URL")
    llm_parser.add_argument("--model", default="gpt-4o-mini", help="模型名称")
    llm_parser.add_argument("--max-tokens", type=int, default=1024, help="最大输出 tokens")
    llm_parser.add_argument("--temperature", type=float, default=0.0, help="温度参数")
    llm_parser.add_argument("--tokenizer", default="cl100k_base", help="分词器")
    llm_parser.add_argument("--chunk-size", type=int, default=1024, help="分块大小")
    llm_parser.add_argument("--chunk-overlap", type=int, default=100, help="分块重叠")
    llm_parser.add_argument("--concurrency", type=int, default=10, help="LLM 并发数")
    llm_parser.add_argument("--storage-concurrency", type=int, default=5, help="存储并发数")
    llm_parser.add_argument("--flush-every-n", type=int, default=0, help="每 N 个条目 flush (0=关闭)")
    llm_parser.add_argument("--do-summary", action="store_true", help="启用实体描述摘要")
    llm_parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # JSON 模式
    json_parser = subparsers.add_parser("json", help="从 JSON 记录构建超图")
    json_parser.add_argument("--input", required=True, help="JSON 记录文件")
    json_parser.add_argument("--output", default="./output", help="输出目录")
    json_parser.add_argument("--chunk-size", type=int, default=2048, help="分块大小")
    json_parser.add_argument("--chunk-overlap", type=int, default=0, help="分块重叠")
    json_parser.add_argument("--storage-concurrency", type=int, default=5, help="存储并发数")
    json_parser.add_argument("--flush-every-n", type=int, default=0, help="每 N 个条目 flush")
    json_parser.add_argument("--verbose", action="store_true", help="详细日志")
    
    # 分析模式
    analyze_parser = subparsers.add_parser("analyze", help="分析已有超图")
    analyze_parser.add_argument("--input", required=True, help="包含 hypergraph.graphml 的目录")
    analyze_parser.add_argument("--show-examples", action="store_true", help="显示示例节点")
    
    args = parser.parse_args()
    
    if not args.mode:
        parser.print_help()
        return 1
    
    # 设置日志
    if hasattr(args, 'verbose') and args.verbose:
        set_logger(if_stream=True)
    
    # 创建输出目录
    if hasattr(args, 'output'):
        os.makedirs(args.output, exist_ok=True)
    
    # 运行对应模式
    if args.mode == "llm":
        success = asyncio.run(run_with_llm(args))
    elif args.mode == "json":
        success = asyncio.run(run_from_json(args))
    elif args.mode == "analyze":
        success = analyze_graph(args)
    else:
        print(f"❌ 未知模式: {args.mode}")
        return 1
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
