name: "Pyl<PERSON>"

on:
  push:
    branches:
      - 'main'
      - 'master'
      - 'release-*'
  pull_request:

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.10", "3.11"]

    steps:
    - uses: actions/checkout@v4
    - name: Setup Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}
        cache: 'pip'

    - name: Install dependencies
      run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pylint

    - name: Run Pylint
      run: |
          pylint --rcfile=.pylintrc baselines/ graphgen/ webui/
